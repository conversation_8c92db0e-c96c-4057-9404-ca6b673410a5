import Cart from "../models/Cart.js";
import productModel from "../models/productModels.js";

// Thêm sản phẩm vào giỏ hàng
const addToCart = async (req, res) => {
  try {
    const { userId, itemId, size, quantity, productData } = req.body;

    // Kiểm tra sản phẩm có tồn tại không
    const product = await productModel.findById(itemId);
    if (!product) {
      return res.json({ success: false, message: "Sản phẩm không tồn tại" });
    }

    // Kiểm tra tồn kho
    if (product.quantity < quantity) {
      return res.json({
        success: false,
        message: `Sản phẩm "${product.name}" chỉ còn ${product.quantity} sản phẩm trong kho`
      });
    }

    const existingItem = await Cart.findOne({ userId, itemId, size });

    if (existingItem) {
      const newTotalQuantity = existingItem.quantity + quantity;

      // <PERSON><PERSON><PERSON> tra tổng số lượng sau khi thêm
      if (newTotalQuantity > product.quantity) {
        return res.json({
          success: false,
          message: `<PERSON>hông thể thêm. Tổng số lượng (${newTotalQuantity}) vượt quá tồn kho (${product.quantity})`
        });
      }

      existingItem.quantity = newTotalQuantity;
      await existingItem.save();
    } else {
      const newItem = new Cart({
        userId,
        itemId,
        name: productData.name,
        image: productData.image,
        originalPrice: productData.originalPrice || productData.price,
        price: productData.price,
        hasPromotion: productData.hasPromotion || false,
        promotion: productData.promotion || null,
        discountPercentage: productData.promotion?.discountPercentage || 0,
        size,
        quantity
      });
      await newItem.save();
    }

    res.json({ success: true, message: "Đã thêm vào giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi thêm vào giỏ hàng" });
  }
};

const removeFromCart = async (req, res) => {
  try {
    const { userId, itemId, size } = req.body;
    await Cart.deleteOne({ userId, itemId, size });

    res.json({ success: true, message: "Đã xóa khỏi giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi xóa khỏi giỏ hàng" });
  }
};

const updateCartQuantity = async (req, res) => {
  try {
    const { userId, itemId, size, quantity } = req.body;

    if (quantity <= 0) {
      await Cart.deleteOne({ userId, itemId, size });
    } else {
      // Kiểm tra sản phẩm có tồn tại không
      const product = await productModel.findById(itemId);
      if (!product) {
        return res.json({ success: false, message: "Sản phẩm không tồn tại" });
      }

      // Kiểm tra tồn kho
      if (product.quantity < quantity) {
        return res.json({
          success: false,
          message: `Sản phẩm "${product.name}" chỉ còn ${product.quantity} sản phẩm trong kho`
        });
      }

      await Cart.updateOne({ userId, itemId, size }, { $set: { quantity } });
    }

    res.json({ success: true, message: "Đã cập nhật giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi cập nhật giỏ hàng" });
  }
};

const getCart = async (req, res) => {
  try {
    const { userId } = req.body;
    const cartItems = await Cart.find({ userId }).sort({ addedAt: -1 });

    res.json({ success: true, cartData: cartItems });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi lấy giỏ hàng" });
  }
};

const clearCart = async (req, res) => {
  try {
    const { userId } = req.body;
    await Cart.deleteMany({ userId });

    res.json({ success: true, message: "Đã xóa toàn bộ giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi xóa giỏ hàng" });
  }
};

const syncCart = async (req, res) => {
  try {
    const { userId, cartData } = req.body;

    // Xóa toàn bộ giỏ cũ
    await Cart.deleteMany({ userId });

    // Thêm mới
    const newItems = cartData.map(item => ({
      userId,
      ...item,
      addedAt: new Date(),
    }));

    await Cart.insertMany(newItems);
    res.json({ success: true, message: "Đã đồng bộ giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Lỗi khi đồng bộ giỏ hàng" });
  }
};
 
export {
  addToCart,
  removeFromCart,
  updateCartQuantity,
  getCart,
  clearCart,
  syncCart
};
