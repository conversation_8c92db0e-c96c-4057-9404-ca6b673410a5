// routes/reviewRoutes.js
import express from 'express';
import auth from '../middleware/auth.js';
import { addReview, getProductReviews, getUserReviews, getAllReviews } from '../controllers/reviewController.js';

const router = express.Router();

// Test route để debug
router.get('/test', (req, res) => {
  console.log("🧪 Test route called");
  res.json({ success: true, message: "Review routes working!", timestamp: new Date() });
});

// Get all reviews (for admin)
router.get('/all', getAllReviews);

// Add review (requires auth)
router.post('/add', auth, addReview);

// Get reviews for a specific product (public)
router.get('/product/:productId', getProductReviews);

// Get reviews by user (requires auth)
router.get('/user', auth, getUserReviews);

export default router;
