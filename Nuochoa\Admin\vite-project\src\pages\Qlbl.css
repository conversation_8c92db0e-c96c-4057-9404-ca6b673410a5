/* Qlbl.css - Styling cho trang quản lý bình luận */

.reviews-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title {
  color: #333;
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 600;
  text-align: center;
}

.reviews-stats {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.reviews-stats p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.loading {
  color: #007bff;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin: 20px;
}

.reviews-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.reviews-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.reviews-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.reviews-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reviews-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #eee;
  vertical-align: top;
}

.reviews-table tbody tr:hover {
  background-color: #f8f9fa;
}

.reviews-table tbody tr:last-child td {
  border-bottom: none;
}

/* User Info Styling */
.user-info strong {
  color: #333;
  font-size: 15px;
}

.user-email {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

/* Product Info Styling */
.product-info strong {
  color: #333;
  font-size: 15px;
}

.product-category {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.product-price {
  color: #28a745;
  font-weight: 600;
  font-size: 13px;
  margin-top: 4px;
}

/* Order Info Styling */
.order-info strong {
  color: #333;
  font-size: 15px;
}

.order-status {
  font-size: 12px;
  margin-top: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  font-weight: 500;
}

.order-status {
  background: #d1ecf1;
  color: #0c5460;
}

.order-amount {
  color: #dc3545;
  font-weight: 600;
  font-size: 13px;
  margin-top: 4px;
}

/* Rating Info Styling */
.rating-info {
  text-align: center;
}

.rating-stars {
  font-size: 18px;
  margin-bottom: 4px;
}

.rating-number {
  color: #666;
  font-size: 12px;
  font-weight: 500;
}

/* Comment Info Styling */
.comment-info {
  max-width: 300px;
}

.comment-text {
  color: #333;
  line-height: 1.4;
  word-wrap: break-word;
  font-style: italic;
}

/* Date Info Styling */
.date-info {
  color: #666;
  font-size: 13px;
  white-space: nowrap;
}

/* No Reviews Styling */
.no-reviews {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 12px;
}

.no-reviews p {
  font-size: 18px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .reviews-table {
    font-size: 13px;
  }
  
  .reviews-table th,
  .reviews-table td {
    padding: 12px 8px;
  }
}

@media (max-width: 768px) {
  .reviews-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .reviews-table-container {
    overflow-x: auto;
  }
  
  .reviews-table {
    min-width: 800px;
  }
  
  .comment-info {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .reviews-table {
    font-size: 12px;
  }
  
  .reviews-table th,
  .reviews-table td {
    padding: 8px 6px;
  }
}
