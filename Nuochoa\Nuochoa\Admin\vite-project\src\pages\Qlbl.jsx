import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Qlbl.css'; // Import CSS file

const Qlbl = () => {
  // State để lưu dữ liệu đánh giá và người dùng
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // useEffect để gọi API khi component render
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        // Gọi API để lấy tất cả bình luận đánh giá
        const response = await axios.get('http://localhost:4000/api/review/all');

        console.log('Reviews response:', response.data); // Debug log

        if (response.data.success && Array.isArray(response.data.reviews)) {
          setReviews(response.data.reviews);  // Lưu kết quả vào state
        } else {
          setError('<PERSON><PERSON> liệu không hợp lệ');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching reviews:', err);
        setError('Lỗi khi tải dữ liệu bình luận: ' + err.message);
        setLoading(false);
      }
    };

    fetchReviews();
  }, []);  // [] để gọi 1 lần khi component render

  // Function để render sao đánh giá
  const renderStars = (rating) => {
    return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  // Function để format ngày tháng
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return <div className="loading">Đang tải dữ liệu...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="reviews-container">
      <h2 className="page-title">Quản lý bình luận đánh giá</h2>
      <div className="reviews-stats">
        <p>Tổng số đánh giá: <strong>{reviews.length}</strong></p>
      </div>

      {reviews.length > 0 ? (
        <div className="reviews-table-container">
          <table className="reviews-table">
            <thead>
              <tr>
                <th>Người dùng</th>
                <th>Sản phẩm</th>
                <th>Đơn hàng</th>
                <th>Đánh giá</th>
                <th>Bình luận</th>
                <th>Ngày đánh giá</th>
              </tr>
            </thead>
            <tbody>
              {reviews.map((review) => (
                <tr key={review._id}>
                  <td className="user-info">
                    <div>
                      <strong>{review.userId?.name || 'N/A'}</strong>
                      {review.userId?.email && (
                        <div className="user-email">{review.userId.email}</div>
                      )}
                    </div>
                  </td>
                  <td className="product-info">
                    <div>
                      <strong>{review.productId?.name || 'N/A'}</strong>
                      {review.productId?.category && (
                        <div className="product-category">{review.productId.category}</div>
                      )}
                      {review.productId?.price && (
                        <div className="product-price">{review.productId.price.toLocaleString('vi-VN')}đ</div>
                      )}
                    </div>
                  </td>
                  <td className="order-info">
                    <div>
                      <strong>#{review.orderId?.orderCode || 'N/A'}</strong>
                      {review.orderId?.status && (
                        <div className="order-status">{review.orderId.status}</div>
                      )}
                      {review.orderId?.amount && (
                        <div className="order-amount">{review.orderId.amount.toLocaleString('vi-VN')}đ</div>
                      )}
                    </div>
                  </td>
                  <td className="rating-info">
                    <div className="rating-stars">
                      {renderStars(review.rating)}
                    </div>
                    <div className="rating-number">({review.rating}/5)</div>
                  </td>
                  <td className="comment-info">
                    <div className="comment-text">
                      {review.comment || 'Không có bình luận'}
                    </div>
                  </td>
                  <td className="date-info">
                    {formatDate(review.date)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="no-reviews">
          <p>Chưa có bình luận đánh giá nào</p>
        </div>
      )}
    </div>
  );
};

export default Qlbl;
