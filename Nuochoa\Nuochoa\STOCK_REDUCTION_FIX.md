# Sửa lỗi tồn kho không giảm khi mua hàng

## 🐛 Vấn đề ban đầu
- Khi khách hàng đặt hàng (cả đăng nhập Google và đăng nhập thường), tồn kho sản phẩm không được giảm
- Đ<PERSON>ều này xảy ra với tất cả các phương thức thanh toán: COD, Stripe, PayOS

## 🔧 Nguyên nhân
1. **COD Orders**: Logic giảm tồn kho không được thực hiện khi tạo đơn hàng COD
2. **Frontend Logic**: Frontend gọi API `reduceStock` riêng biệt sau khi đặt hàng, có thể thất bại do authentication
3. **Validation thiếu**: Không kiểm tra tồn kho trước khi tạo đơn hàng
4. **Inconsistent Logic**: Logic xử lý khác nhau giữa các phương thức thanh toán

## ✅ C<PERSON>c thay đổi đã thực hiện

### 1. Backend Changes

#### `controllers/orderController.js`
- **PlaceOder function**: 
  - Thêm validation tồn kho trước khi tạo đơn hàng
  - Xử lý COD orders: giảm tồn kho ngay khi tạo đơn hàng thành công
  - Thêm ghi doanh thu realtime cho COD orders

- **placeOrderCOD function**:
  - Thêm validation tồn kho
  - Giảm tồn kho ngay khi tạo đơn hàng COD
  - Thêm ghi doanh thu realtime

- **verifyOrder function**:
  - Cập nhật logic xử lý COD để tránh xử lý trùng lặp
  - COD orders đã được xử lý khi tạo, chỉ cần xác nhận trạng thái

- **payWithPayOS function**:
  - Thêm validation tồn kho
  - Sử dụng userId từ middleware auth thay vì request body

#### `routes/orderRoutes.js`
- Thêm authentication cho route `/payos`
- Thêm authentication cho route `/reduceStock`

### 2. Frontend Changes

#### `pages/Checkout/Checkout.jsx`
- **handleCOD**: Loại bỏ việc gọi API `reduceStock` riêng biệt vì backend đã xử lý
- **handleStripe**: Thêm `payment: 'Stripe'` parameter
- **handlePayOS**: 
  - Thêm `payment: 'PayOS'` parameter
  - Loại bỏ `userId` từ request body (sẽ được lấy từ middleware)
  - Sử dụng header `token` thay vì `Authorization`

## 🔄 Flow xử lý mới

### COD Orders
1. Frontend gửi request đến `/api/order/placeCOD`
2. Backend validation tồn kho
3. Tạo đơn hàng với status "Processing"
4. **Giảm tồn kho ngay lập tức**
5. Ghi doanh thu realtime
6. Trả về response thành công

### Online Payment (Stripe/PayOS)
1. Frontend gửi request đến `/api/order/place` hoặc `/api/order/payos`
2. Backend validation tồn kho
3. Tạo đơn hàng với status "pending"
4. Tạo payment session/link
5. **Tồn kho được giảm khi webhook xác nhận thanh toán thành công**

## 🧪 Testing

### Manual Testing
1. Kiểm tra tồn kho sản phẩm trước khi đặt hàng
2. Đặt hàng với COD
3. Kiểm tra tồn kho sau khi đặt hàng → phải giảm ngay lập tức
4. Đặt hàng với Stripe/PayOS
5. Hoàn thành thanh toán
6. Kiểm tra tồn kho sau khi thanh toán thành công → phải giảm

### Automated Testing
- File `test_stock_reduction.js` đã được tạo để test tự động
- Cần token hợp lệ để chạy test

## 🔒 Security Improvements
- Tất cả các API đặt hàng đều yêu cầu authentication
- Validation tồn kho ngăn chặn overselling
- Sử dụng middleware auth thông minh hỗ trợ cả JWT và Firebase tokens

## 📊 Monitoring
- Thêm console logs để theo dõi quá trình giảm tồn kho
- Ghi doanh thu realtime cho tất cả các loại đơn hàng
- Log chi tiết cho debugging

## ⚠️ Lưu ý quan trọng
1. **COD orders** giờ đây giảm tồn kho ngay khi tạo đơn hàng
2. **Online payments** vẫn giảm tồn kho khi webhook xác nhận thanh toán
3. Tất cả đơn hàng đều được validation tồn kho trước khi tạo
4. Frontend không cần gọi API `reduceStock` riêng biệt nữa

## 🚀 Deployment Checklist
- [ ] Deploy backend changes
- [ ] Deploy frontend changes  
- [ ] Test với cả đăng nhập Google và đăng nhập thường
- [ ] Test tất cả phương thức thanh toán (COD, Stripe, PayOS)
- [ ] Kiểm tra webhook hoạt động đúng
- [ ] Monitor logs để đảm bảo không có lỗi
