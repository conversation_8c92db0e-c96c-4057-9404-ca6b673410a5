// Test script để kiểm tra việc giảm tồn kho
import axios from 'axios';

const BASE_URL = 'http://localhost:4000';

// Test data
const testProduct = {
  name: 'Test Product',
  price: 100000,
  importPrice: 50000,
  category: 'Test',
  brand: 'Test Brand',
  sizes: ['M', 'L'],
  description: 'Test product for stock reduction',
  quantity: 10
};

const testOrder = {
  address: {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    street: '123 Test St',
    city: 'Test City',
    state: 'Test State',
    zipcode: '12345',
    country: 'VN',
    phone: '0123456789'
  },
  items: [],
  amount: 120000, // 100000 + 20000 shipping
  payment: 'COD'
};

async function testStockReduction() {
  try {
    console.log('🧪 Starting stock reduction test...');
    
    // 1. Tạo sản phẩm test (cần admin token)
    console.log('📦 Creating test product...');
    // Note: Cần implement tạo sản phẩm test hoặc sử dụng sản phẩm có sẵn
    
    // 2. L<PERSON><PERSON> danh sách sản phẩm để test
    const productsResponse = await axios.get(`${BASE_URL}/api/product/list`);
    if (!productsResponse.data.success || productsResponse.data.products.length === 0) {
      console.log('❌ No products found for testing');
      return;
    }
    
    const testProductFromDB = productsResponse.data.products[0];
    console.log('📦 Using product:', testProductFromDB.name, 'Current stock:', testProductFromDB.quantity);
    
    if (testProductFromDB.quantity < 2) {
      console.log('❌ Product stock too low for testing');
      return;
    }
    
    // 3. Tạo đơn hàng test với COD
    testOrder.items = [{
      _id: testProductFromDB._id,
      name: testProductFromDB.name,
      price: testProductFromDB.price,
      quantity: 1,
      image: testProductFromDB.image
    }];
    
    // Note: Cần token hợp lệ để test
    const token = 'test_token'; // Thay bằng token thực tế
    
    console.log('🛒 Creating COD order...');
    const orderResponse = await axios.post(`${BASE_URL}/api/order/placeCOD`, testOrder, {
      headers: { token }
    });
    
    if (orderResponse.data.success) {
      console.log('✅ COD order created successfully:', orderResponse.data.orderId);
      
      // 4. Kiểm tra tồn kho sau khi đặt hàng
      const updatedProductResponse = await axios.get(`${BASE_URL}/api/product/list`);
      const updatedProduct = updatedProductResponse.data.products.find(p => p._id === testProductFromDB._id);
      
      console.log('📊 Stock before order:', testProductFromDB.quantity);
      console.log('📊 Stock after order:', updatedProduct.quantity);
      console.log('📊 Expected reduction:', testOrder.items[0].quantity);
      console.log('📊 Actual reduction:', testProductFromDB.quantity - updatedProduct.quantity);
      
      if (testProductFromDB.quantity - updatedProduct.quantity === testOrder.items[0].quantity) {
        console.log('✅ Stock reduction test PASSED');
      } else {
        console.log('❌ Stock reduction test FAILED');
      }
    } else {
      console.log('❌ Failed to create COD order:', orderResponse.data.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

// Chạy test
console.log('🚀 Stock Reduction Test Suite');
console.log('================================');
console.log('This test will:');
console.log('1. Find a product with sufficient stock');
console.log('2. Create a COD order');
console.log('3. Verify stock is reduced correctly');
console.log('');
console.log('⚠️  Note: You need a valid token to run this test');
console.log('⚠️  Make sure the server is running on localhost:4000');
console.log('');

// Uncomment to run the test
// testStockReduction();

export default testStockReduction;
